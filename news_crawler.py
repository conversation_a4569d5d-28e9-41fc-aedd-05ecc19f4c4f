#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻爬虫脚本 - 基于newsnow项目的Python实现
支持多个主流平台的热点新闻抓取
"""

import json
import re
import time
from typing import Dict, List, Optional, Any
from urllib.parse import quote, urlencode
import requests
from bs4 import BeautifulSoup
import feedparser
from datetime import datetime

# 导入配置
try:
    from crawler_config import PLATFORM_LIMITS, NETWORK_CONFIG, PROXY_CONFIG, OUTPUT_CONFIG, PLATFORM_ENABLED
except ImportError:
    # 如果没有配置文件，使用默认配置
    PLATFORM_LIMITS = {'zhihu': 20, 'bilibili': 30, 'github': 20, 'v2ex': 20, 'ithome': 20}
    NETWORK_CONFIG = {'timeout': 10, 'max_retries': 3, 'retry_delay': 1, 'request_interval': 1}
    PROXY_CONFIG = {'enabled': False}
    OUTPUT_CONFIG = {'save_to_file': True, 'show_summary': True, 'summary_preview_count': 5}
    PLATFORM_ENABLED = {'weibo': True, 'baidu': True, 'zhihu': True, 'toutiao': True, 'bilibili': True, 'github': True, 'v2ex': True, 'ithome': True}


class NewsCrawler:
    """新闻爬虫主类"""
    
    def __init__(self, proxy: Optional[str] = None, timeout: int = None):
        """
        初始化爬虫

        Args:
            proxy: 代理地址，格式如 "http://127.0.0.1:7890"
            timeout: 请求超时时间（秒）
        """
        self.session = requests.Session()
        self.timeout = timeout or NETWORK_CONFIG['timeout']
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
        })
        
        # 设置代理
        if proxy:
            self.session.proxies = {
                'http': proxy,
                'https': proxy
            }
        elif PROXY_CONFIG.get('enabled'):
            self.session.proxies = {
                'http': PROXY_CONFIG.get('http_proxy'),
                'https': PROXY_CONFIG.get('https_proxy')
            }
    
    def _fetch_with_retry(self, url: str, max_retries: int = 3, **kwargs) -> requests.Response:
        """带重试的请求方法"""
        for attempt in range(max_retries + 1):
            try:
                response = self.session.get(url, timeout=self.timeout, **kwargs)
                response.raise_for_status()
                return response
            except Exception as e:
                if attempt == max_retries:
                    raise e
                print(f"请求失败，正在重试 ({attempt + 1}/{max_retries}): {e}")
                time.sleep(2 ** attempt)  # 指数退避
    
    def weibo_hot_search(self) -> List[Dict[str, Any]]:
        """微博热搜"""
        try:
            url = "https://weibo.com/ajax/side/hotSearch"
            response = self._fetch_with_retry(url)
            data = response.json()
            
            results = []
            for item in data.get('data', {}).get('realtime', []):
                if item.get('is_ad'):  # 跳过广告
                    continue
                
                keyword = item.get('word_scheme') or f"#{item.get('word')}#"
                results.append({
                    'id': item.get('word'),
                    'title': item.get('word'),
                    'url': f"https://s.weibo.com/weibo?q={quote(keyword)}",
                    'mobile_url': f"https://m.weibo.cn/search?containerid=231522type%3D1%26q%3D{quote(keyword)}&_T_WM=16922097837&v_p=42",
                    'extra': {
                        'icon': item.get('icon'),
                        'rank': item.get('rank')
                    }
                })
            
            print(f"✅ 微博热搜: 获取到 {len(results)} 条数据")
            return results
            
        except Exception as e:
            print(f"❌ 微博热搜获取失败: {e}")
            return []
    
    def baidu_hot_search(self) -> List[Dict[str, Any]]:
        """百度热搜"""
        try:
            url = "https://top.baidu.com/board?tab=realtime"
            response = self._fetch_with_retry(url)
            
            # 从HTML中提取JSON数据
            html_content = response.text
            json_match = re.search(r'<!--s-data:(.*?)-->', html_content, re.DOTALL)
            if not json_match:
                raise ValueError("无法从页面中提取数据")
            
            data = json.loads(json_match.group(1))
            
            results = []
            content_list = data.get('data', {}).get('cards', [{}])[0].get('content', [])
            
            for item in content_list:
                if item.get('isTop'):  # 跳过置顶
                    continue
                
                results.append({
                    'id': item.get('rawUrl'),
                    'title': item.get('word'),
                    'url': item.get('rawUrl'),
                    'extra': {
                        'hover': item.get('desc')
                    }
                })
            
            print(f"✅ 百度热搜: 获取到 {len(results)} 条数据")
            return results
            
        except Exception as e:
            print(f"❌ 百度热搜获取失败: {e}")
            return []
    
    def zhihu_hot_list(self) -> List[Dict[str, Any]]:
        """知乎热榜"""
        try:
            limit = PLATFORM_LIMITS.get('zhihu', 20)
            url = f"https://www.zhihu.com/api/v3/feed/topstory/hot-list-web?limit={limit}&desktop=true"
            response = self._fetch_with_retry(url)
            data = response.json()
            
            results = []
            for item in data.get('data', []):
                target = item.get('target', {})
                link = target.get('link', {})
                
                # 提取ID
                url_match = re.search(r'(\d+)$', link.get('url', ''))
                item_id = url_match.group(1) if url_match else link.get('url')
                
                results.append({
                    'id': item_id,
                    'title': target.get('title_area', {}).get('text'),
                    'url': link.get('url'),
                    'extra': {
                        'info': target.get('metrics_area', {}).get('text'),
                        'hover': target.get('excerpt_area', {}).get('text')
                    }
                })
            
            print(f"✅ 知乎热榜: 获取到 {len(results)} 条数据")
            return results
            
        except Exception as e:
            print(f"❌ 知乎热榜获取失败: {e}")
            return []
    
    def toutiao_hot(self) -> List[Dict[str, Any]]:
        """今日头条热榜"""
        try:
            url = "https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc"
            response = self._fetch_with_retry(url)
            data = response.json()
            
            results = []
            for item in data.get('data', []):
                results.append({
                    'id': item.get('ClusterIdStr'),
                    'title': item.get('Title'),
                    'url': f"https://www.toutiao.com/trending/{item.get('ClusterIdStr')}/",
                    'extra': {
                        'hot_value': item.get('HotValue'),
                        'icon': item.get('LabelUri', {}).get('url') if item.get('LabelUri') else None
                    }
                })
            
            print(f"✅ 今日头条: 获取到 {len(results)} 条数据")
            return results
            
        except Exception as e:
            print(f"❌ 今日头条获取失败: {e}")
            return []
    
    def bilibili_hot_search(self) -> List[Dict[str, Any]]:
        """B站热搜"""
        try:
            limit = PLATFORM_LIMITS.get('bilibili', 30)
            url = f"https://s.search.bilibili.com/main/hotword?limit={limit}"
            response = self._fetch_with_retry(url)
            data = response.json()
            
            results = []
            for item in data.get('list', []):
                results.append({
                    'id': item.get('keyword'),
                    'title': item.get('show_name'),
                    'url': f"https://search.bilibili.com/all?keyword={quote(item.get('keyword', ''))}",
                    'extra': {
                        'icon': item.get('icon'),
                        'score': item.get('score')
                    }
                })
            
            print(f"✅ B站热搜: 获取到 {len(results)} 条数据")
            return results
            
        except Exception as e:
            print(f"❌ B站热搜获取失败: {e}")
            return []
    
    def github_trending(self) -> List[Dict[str, Any]]:
        """GitHub趋势"""
        try:
            url = "https://api.github.com/search/repositories"
            params = {
                'q': f'created:>{datetime.now().strftime("%Y-%m-%d")}',
                'sort': 'stars',
                'order': 'desc',
                'per_page': PLATFORM_LIMITS.get('github', 20)
            }
            
            response = self._fetch_with_retry(url, params=params)
            data = response.json()
            
            results = []
            for item in data.get('items', []):
                results.append({
                    'id': item.get('id'),
                    'title': item.get('full_name'),
                    'url': item.get('html_url'),
                    'extra': {
                        'description': item.get('description'),
                        'stars': item.get('stargazers_count'),
                        'language': item.get('language')
                    }
                })
            
            print(f"✅ GitHub趋势: 获取到 {len(results)} 条数据")
            return results
            
        except Exception as e:
            print(f"❌ GitHub趋势获取失败: {e}")
            return []

    def v2ex_hot(self) -> List[Dict[str, Any]]:
        """V2EX热门"""
        try:
            # V2EX使用RSS源
            feeds = ['create', 'ideas', 'programmer', 'share']
            all_items = []

            for feed in feeds:
                try:
                    url = f"https://www.v2ex.com/feed/{feed}.json"
                    response = self._fetch_with_retry(url)
                    data = response.json()

                    for item in data.get('items', []):
                        all_items.append({
                            'id': item.get('id'),
                            'title': item.get('title'),
                            'url': item.get('url'),
                            'extra': {
                                'date': item.get('date_modified') or item.get('date_published'),
                                'feed': feed
                            }
                        })
                except Exception as e:
                    print(f"V2EX {feed} 获取失败: {e}")

            # 按时间排序
            all_items.sort(key=lambda x: x.get('extra', {}).get('date', ''), reverse=True)
            limit = PLATFORM_LIMITS.get('v2ex', 20)
            results = all_items[:limit]

            print(f"✅ V2EX热门: 获取到 {len(results)} 条数据")
            return results

        except Exception as e:
            print(f"❌ V2EX热门获取失败: {e}")
            return []

    def ithome_news(self) -> List[Dict[str, Any]]:
        """IT之家新闻"""
        try:
            url = "https://www.ithome.com/"
            response = self._fetch_with_retry(url)
            soup = BeautifulSoup(response.text, 'html.parser')

            results = []
            # 查找新闻列表
            limit = PLATFORM_LIMITS.get('ithome', 20)
            news_items = soup.select('.lst li')[:limit]

            for item in news_items:
                link = item.select_one('a')
                if link:
                    title = link.get('title') or link.text.strip()
                    href = link.get('href')

                    if href and title:
                        # 处理相对链接
                        if href.startswith('/'):
                            href = f"https://www.ithome.com{href}"

                        results.append({
                            'id': href,
                            'title': title,
                            'url': href,
                            'extra': {}
                        })

            print(f"✅ IT之家: 获取到 {len(results)} 条数据")
            return results

        except Exception as e:
            print(f"❌ IT之家获取失败: {e}")
            return []

    def get_all_news(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有平台的新闻"""
        print("🚀 开始抓取新闻数据...")
        print("=" * 50)

        all_sources = {
            'weibo': self.weibo_hot_search,
            'baidu': self.baidu_hot_search,
            'zhihu': self.zhihu_hot_list,
            'toutiao': self.toutiao_hot,
            'bilibili': self.bilibili_hot_search,
            'github': self.github_trending,
            'v2ex': self.v2ex_hot,
            'ithome': self.ithome_news,
        }

        # 根据配置过滤启用的平台
        sources = {name: func for name, func in all_sources.items()
                  if PLATFORM_ENABLED.get(name, True)}

        results = {}
        for source_name, source_func in sources.items():
            try:
                results[source_name] = source_func()
                time.sleep(NETWORK_CONFIG.get('request_interval', 1))  # 避免请求过快
            except Exception as e:
                print(f"❌ {source_name} 抓取失败: {e}")
                results[source_name] = []

        print("=" * 50)
        print("📊 抓取完成!")

        # 统计信息
        total_items = sum(len(items) for items in results.values())
        print(f"总计获取 {total_items} 条新闻数据")

        return results

    def save_to_file(self, data: Dict[str, List[Dict[str, Any]]], filename: str = None):
        """保存数据到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"news_data_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 数据已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")

    def print_summary(self, data: Dict[str, List[Dict[str, Any]]]):
        """打印数据摘要"""
        print("\n📋 数据摘要:")
        print("-" * 50)

        for source_name, items in data.items():
            print(f"\n🔸 {source_name.upper()} ({len(items)}条):")
            for i, item in enumerate(items[:5], 1):  # 只显示前5条
                title = item.get('title', '无标题')
                if len(title) > 40:
                    title = title[:40] + "..."
                print(f"  {i}. {title}")

            if len(items) > 5:
                print(f"  ... 还有 {len(items) - 5} 条")


def main():
    """主函数"""
    print("🎯 新闻爬虫启动")
    print("基于newsnow项目的Python实现")
    print("=" * 50)

    # 创建爬虫实例
    # 如果需要使用代理，取消下面的注释并设置代理地址
    # crawler = NewsCrawler(proxy="http://127.0.0.1:7890")
    crawler = NewsCrawler()

    # 获取所有新闻
    news_data = crawler.get_all_news()

    # 根据配置保存到文件
    if OUTPUT_CONFIG.get('save_to_file', True):
        crawler.save_to_file(news_data)

    # 根据配置打印摘要
    if OUTPUT_CONFIG.get('show_summary', True):
        crawler.print_summary(news_data)

    print("\n✨ 程序执行完成!")


if __name__ == "__main__":
    main()
